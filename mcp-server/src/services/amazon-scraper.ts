/**
 * Amazon Scraping Service
 * 
 * This service handles all Amazon scraping operations including product search,
 * product details extraction, and image fetching.
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import { AxiosConfig } from '../types/index';
import userAgentService from './user-agent';
import proxyService from './proxy.js';
import logger from './logger.js';

// Amazon base URL
const AMAZON_BASE_URL = "https://www.amazon.sg";

export interface ProductSearchOptions {
  topic: string;
  limit?: number;
  lowPrice?: number;
  highPrice?: number;
}

export interface ProductSearchResult {
  products: Array<{
    asin: string;
    title: string;
    price: number | null;
    rating: string;
    review: string;
  }>;
  totalFound: number;
}

export interface ProductDetails {
  title: string;
  price: number | null;
  currency: string;
  rating: string;
  reviewCount: string;
  mainImage: string;
  features: string[];
  url: string;
  content: string;
  description: string;
  specifications: string[];
  aboutThisItem: string[];
}

export interface ImageFetchResult {
  imageData: string;
  mimeType: string;
}

/**
 * Amazon Scraping Service Class
 */
class AmazonScraperService {
  /**
   * Creates axios configuration with user agent and proxy settings
   */
  private async createAxiosConfig(responseType?: 'arraybuffer' | 'json' | 'text'): Promise<AxiosConfig> {
    const userAgent = userAgentService.getRotatedUserAgent();
    const proxyConfig = proxyService.isEnabled()
      ? await proxyService.getProxyConfig()
      : null;

    const config: AxiosConfig = {
      headers: {
        "User-Agent": userAgent,
      },
      timeout: 30000,
    };

    if (proxyConfig) {
      config.proxy = proxyConfig;
    }

    if (responseType) {
      config.responseType = responseType;
    }

    return config;
  }

  /**
   * Search for products on Amazon
   */
  async searchProducts(options: ProductSearchOptions): Promise<ProductSearchResult> {
    const { topic, limit = 10, lowPrice, highPrice } = options;

    // Build search URL with price filters if provided
    let searchUrl = `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`;
    if (lowPrice !== undefined) {
      searchUrl += `&low-price=${lowPrice}`;
    }
    if (highPrice !== undefined) {
      searchUrl += `&high-price=${highPrice}`;
    }

    logger.logScrapingAttempt({
      topic,
      url: searchUrl,
    });

    try {
      const axiosConfig = await this.createAxiosConfig();
      const response = await axios.get<string>(searchUrl, axiosConfig);

      logger.logScrapingSuccess({
        topic,
        url: searchUrl,
        statusCode: response.status,
        responseTime: Date.now(),
        userAgent: axiosConfig.headers['User-Agent'],
        proxyUrl: axiosConfig.proxy?.host,
      });

      // Parse HTML with Cheerio
      const $ = cheerio.load(response.data);

      // Extract product information from the page
      const products: Array<{
        asin: string;
        title: string;
        price: number | null;
        rating: string;
        review: string;
      }> = [];

      // Look for elements with data-asin attribute (Amazon's product containers)
      $("[role=listitem][data-asin]").each((_index, element) => {
        const asin = $(element).attr("data-asin");

        // Only process valid ASINs and respect the limit
        if (asin && asin.trim() !== "" && products.length < limit) {
          const $product = $(element);

          // Extract title
          const title = $product.find('h2 a span, .a-size-mini span, .a-size-base-plus').first().text().trim() || 'Title not found';

          // Extract price
          const priceText = $product.find('.a-price-whole, .a-price .a-offscreen').first().text().trim();
          const price = priceText ? parseFloat(priceText.replace(/[^\d.]/g, '')) : null;

          // Extract rating
          const ratingText = $product.find('.a-icon-alt').first().text();
          const rating = ratingText.match(/[\d.]+/)?.[0] || '';

          // Extract review count
          const reviewText = $product.find('.a-size-base').filter((_, el) => $(el).text().includes('(')).text();
          const review = reviewText.match(/\(([^)]+)\)/)?.[1] || '';

          products.push({
            asin,
            title,
            price,
            rating,
            review
          });
        }
      });

      return {
        products,
        totalFound: products.length,
      };
    } catch (error: any) {
      logger.logScrapingFailure({
        topic,
        url: searchUrl,
        error: error.message,
        responseTime: Date.now(),
        statusCode: error.response?.status,
      });

      throw new Error(`Failed to search products: ${error.message}`);
    }
  }

  /**
   * Get detailed information about a specific product
   */
  async getProductDetails(asin: string): Promise<ProductDetails> {
    const productUrl = `${AMAZON_BASE_URL}/dp/${asin}`;

    logger.logScrapingAttempt({
      topic: `Product details for ${asin}`,
      url: productUrl,
    });

    try {
      const axiosConfig = await this.createAxiosConfig();
      const response = await axios.get<string>(productUrl, axiosConfig);

      logger.logScrapingSuccess({
        topic: `Product details for ${asin}`,
        url: productUrl,
        statusCode: response.status,
        responseTime: Date.now(),
        userAgent: axiosConfig.headers['User-Agent'],
        proxyUrl: axiosConfig.proxy?.host,
      });

      // Parse HTML with Cheerio
      const $ = cheerio.load(response.data);

      // Extract product details
      const title = $("#productTitle").text().trim();
      const priceWhole = $(".a-price-whole").first().text().trim();
      const priceFraction = $(".a-price-fraction").first().text().trim();
      const priceString = priceWhole + priceFraction;
      const price = priceString
        ? parseFloat(priceString.replace(/[^\d.]/g, ""))
        : null;
      const currency = $(".a-price-symbol").first().text().trim();
      const rating =
        $(".a-icon-alt")
          .first()
          .text()
          .match(/[\d.]+/)?.[0] || "";
      const reviewCount =
        $(".a-size-base")
          .filter((_, el) => $(el).text().includes("ratings"))
          .text()
          .match(/[\d,]+/)?.[0] || "";

      // Extract main product image
      const mainImage =
        $("#landingImage").attr("src") ||
        $(".a-dynamic-image").first().attr("src") ||
        "";

      // Extract product features/bullets
      const features: string[] = [];
      $("#feature-bullets ul li span").each((_, element) => {
        const feature = $(element).text().trim();
        if (feature && !feature.includes("Make sure") && feature.length > 10) {
          features.push(feature);
        }
      });

      // Extract product description
      const description = $("#productDescription p, #aplus_feature_div p, #productDescription_feature_div p")
        .map((_, el) => $(el).text().trim())
        .get()
        .filter(text => text.length > 20)
        .join(" ")
        .substring(0, 1000); // Limit to 1000 characters

      // Extract "About this item" section
      const aboutThisItem: string[] = [];
      $("#feature-bullets ul li span, #aplus_feature_div ul li, .a-unordered-list .a-list-item").each((_, element) => {
        const item = $(element).text().trim();
        if (item && item.length > 15 && !item.includes("Make sure") && !item.includes("See more")) {
          aboutThisItem.push(item);
        }
      });

      // Extract product specifications
      const specifications: string[] = [];
      $("#productDetails_techSpec_section_1 tr, #productDetails_detailBullets_sections1 tr, .a-keyvalue tr").each((_, element) => {
        const $row = $(element);
        const key = $row.find("td:first-child, th:first-child").text().trim();
        const value = $row.find("td:last-child, td:nth-child(2)").text().trim();
        if (key && value && key.length > 2 && value.length > 2) {
          specifications.push(`${key}: ${value}`);
        }
      });

      // Compile comprehensive content
      const contentParts: string[] = [];

      if (title) contentParts.push(`Title: ${title}`);
      if (description) contentParts.push(`Description: ${description}`);
      if (features.length > 0) contentParts.push(`Features: ${features.join("; ")}`);
      if (aboutThisItem.length > 0) contentParts.push(`About this item: ${aboutThisItem.slice(0, 5).join("; ")}`);
      if (specifications.length > 0) contentParts.push(`Specifications: ${specifications.slice(0, 10).join("; ")}`);

      const content = contentParts.join("\n\n").substring(0, 4000); // Limit to 4000 characters as per requirements

      return {
        title: title || "Not found",
        price,
        currency: currency || "",
        rating: rating || "",
        reviewCount: reviewCount || "",
        mainImage: mainImage || "",
        features,
        url: productUrl,
        content,
        description: description || "",
        specifications,
        aboutThisItem,
      };
    } catch (error: any) {
      logger.logScrapingFailure({
        topic: `Product details for ${asin}`,
        url: productUrl,
        error: error.message,
        responseTime: Date.now(),
        statusCode: error.response?.status,
      });

      throw new Error(`Failed to get product details: ${error.message}`);
    }
  }

  /**
   * Fetch product image data
   */
  async fetchProductImage(imageUrl: string): Promise<ImageFetchResult> {
    try {
      const axiosConfig = await this.createAxiosConfig('arraybuffer');
      const response = await axios.get(imageUrl, axiosConfig);
      
      const imageData = Buffer.from(response.data as ArrayBuffer).toString('base64');
      const mimeType = response.headers["content-type"] || "image/jpeg";

      return {
        imageData,
        mimeType,
      };
    } catch (error: any) {
      throw new Error(`Failed to fetch image: ${error.message}`);
    }
  }
}

// Export singleton instance
const amazonScraperService = new AmazonScraperService();
export default amazonScraperService;
