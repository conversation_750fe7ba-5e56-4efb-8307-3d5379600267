/**
 * MCP Tool Handler: Search Amazon Products
 * 
 * This handler implements the search-amazon-products MCP tool that searches
 * for products on Amazon.sg and returns product ASINs.
 */

import { z } from 'zod';
import { ProductSearchResult } from '../types/index.js';
import { createSuccessResponse, createErrorResponse } from '../utils/response.js';
import amazonScraperService from '../services/amazon-scraper.js';
import prisma from '../lib/prisma.js';

/**
 * Input schema for the search-amazon-products tool
 */
export const searchProductsSchema = {
  topic: z.string().describe("Search query/topic for products"),
  limit: z
    .number()
    .min(1)
    .max(50)
    .default(10)
    .describe("Maximum number of products to return (1-50)"),
  lowPrice: z
    .number()
    .min(0)
    .optional()
    .describe("Minimum price filter (optional)"),
  highPrice: z
    .number()
    .min(0)
    .optional()
    .describe("Maximum price filter (optional)"),
};

/**
 * Handler function for the search-amazon-products tool
 *
 * @param args - Tool input parameters
 * @returns MCP tool response with search results
 */
export async function handleSearchProducts(args: { topic: string; limit?: number; lowPrice?: number; highPrice?: number }) {
  const { topic, limit = 10, lowPrice, highPrice } = args;

  try {
    // Search for products using the Amazon scraper service
    const searchResult = await amazonScraperService.searchProducts({
      topic,
      limit,
      lowPrice,
      highPrice,
    });

    // Save products to the database, ignoring duplicates
    await prisma.product.createMany({
      data: searchResult.products.map(({ asin }) => ({ asin })),
      skipDuplicates: true,
    });

    // Create search query record
    await prisma.searchQuery.create({
      data: {
        topic,
        limit,
        products: {
          connect: searchResult.products.map(({ asin }) => ({ asin })),
        },
      },
    });

    // Format response according to MCP standards
    const response: ProductSearchResult = {
      success: true,
      searchTerm: topic,
      totalFound: searchResult.totalFound,
      products: searchResult.products.map((p, i) => ({
        asin: p.asin,
        position: i + 1,
        title: p.title,
        price: p.price,
        rating: p.rating,
        review: p.review,
      })),
      message: `Found ${searchResult.totalFound} products for "${topic}". Use the "get-product-details" tool with any of these ASINs to get detailed product information.`,
    };

    return createSuccessResponse(response);
  } catch (error: any) {
    return createErrorResponse(`Error searching for products: ${error.message}`, {
      topic,
      limit,
      lowPrice,
      highPrice,
    });
  }
}

/**
 * Tool configuration for registration with MCP server
 */
export const searchProductsToolConfig = {
  name: "search-amazon-products",
  description: {
    title: "Search Amazon Products",
    description:
      "Search for products on Amazon.sg with optional price filtering and return product information including ASINs, titles, prices, ratings, and reviews. Returns a JSON object with success status, search term, total found count, and array of products with detailed information.",
    inputSchema: searchProductsSchema,
  },
  handler: handleSearchProducts,
};
