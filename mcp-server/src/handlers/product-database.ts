/**
 * MCP Resource Handler: Product Database
 * 
 * This handler implements the product-database MCP resource that provides
 * access to stored product information.
 */

import { ProductDatabaseItem } from '../types/index.js';
import { createResourceResponse, createResourceErrorResponse } from '../utils/response.js';
import prisma from '../lib/prisma.js';

/**
 * Handler function for the product-database resource
 * 
 * @param uri - Resource URI
 * @returns Resource response with product database
 */
export async function handleProductDatabase(uri: URL) {
  try {
    const products = await prisma.product.findMany({
      take: 100,
      orderBy: { updatedAt: "desc" },
    });

    const productDatabase = {
      success: true,
      totalProducts: products.length,
      products: products.map((product): ProductDatabaseItem => ({
        asin: product.asin,
        title: product.title,
        description: product.description,
        content: product.content,
        features: product.features || [],
        specifications: product.specifications || [],
        aboutThisItem: product.aboutThisItem || [],
        rating: product.rating,
        reviewCount: product.reviewCount,
        price: product.price,
        currency: product.currency,
        mainImageUrl: product.mainImageUrl,
        productUrl: product.productUrl,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      })),
    };

    return createResourceResponse(uri.href, productDatabase);
  } catch (error: any) {
    return createResourceErrorResponse(uri.href, error.message);
  }
}

/**
 * Resource configuration for registration with MCP server
 */
export const productDatabaseResourceConfig = {
  name: "product-database",
  uri: "products://database",
  description: {
    title: "Product Database",
    description: "All stored Amazon product information",
    mimeType: "application/json",
  },
  handler: handleProductDatabase,
};
