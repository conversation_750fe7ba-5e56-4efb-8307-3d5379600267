# Amazon Content Pipeline MCP Server

A specialized Model Context Protocol (MCP) server for Amazon.sg marketplace integration, providing comprehensive product data extraction and structured content generation capabilities for automated content pipelines.

## 🚀 Features

### Core Functionality
- **Advanced Product Search**: Search Amazon.sg with price filtering and topic-based queries
- **Enhanced Content Extraction**: Comprehensive product data extraction including:
  - Product descriptions and features
  - Technical specifications
  - Customer ratings and reviews
  - Product highlights ("About this item")
  - Structured content compilation (up to 4000 characters)
- **Image Processing**: Download and serve product images with proper content type handling
- **MCP Protocol**: Full Model Context Protocol implementation for AI agent integration

### Infrastructure & Reliability
- **Proxy Rotation**: Advanced proxy management to avoid IP restrictions
- **User Agent Rotation**: Intelligent user agent rotation for scraping reliability
- **Structured Database**: PostgreSQL with enhanced schema for content generation
- **Type Safety**: Full TypeScript implementation with comprehensive validation
- **Comprehensive Testing**: Unit, integration, and E2E test coverage
- **Docker Support**: Production-ready containerization

### Content Generation Ready
- **Structured Data Storage**: Arrays for features, specifications, and highlights
- **Token-Optimized Content**: Content fields optimized for AI model consumption
- **Comprehensive Product Profiles**: All data needed for article and review generation
- **Database-Backed Caching**: Efficient storage and retrieval of product information

## 🔌 MCP Tools & Resources

### MCP Tools

#### `search-amazon-products`
Search for products on Amazon.sg with advanced filtering options.

**Input:**
```json
{
  "topic": "ergonomic office chairs",
  "limit": 10,
  "lowPrice": 100,
  "highPrice": 2000
}
```

**Output:**
```json
{
  "success": true,
  "searchTerm": "ergonomic office chairs",
  "totalFound": 8,
  "products": [
    {
      "asin": "B081H3Y55N",
      "position": 1,
      "title": "Herman Miller Aeron Chair",
      "price": 1599.00,
      "rating": "4.5",
      "review": "1,234 ratings"
    }
  ]
}
```

#### `get-product-details`
Extract comprehensive product information with enhanced content.

**Input:**
```json
{
  "asin": "B081H3Y55N"
}
```

**Enhanced Output:**
```json
{
  "success": true,
  "asin": "B081H3Y55N",
  "product": {
    "title": "Herman Miller Aeron Chair",
    "price": {
      "amount": 1599.00,
      "currency": "SGD",
      "formatted": "SGD 1599.00"
    },
    "rating": {
      "value": "4.5",
      "formatted": "4.5/5"
    },
    "reviews": {
      "count": "1,234",
      "formatted": "1,234 ratings"
    },
    "images": {
      "main": "https://m.media-amazon.com/images/I/image1.jpg"
    },
    "features": [
      "Ergonomic design for all-day comfort",
      "Breathable mesh material",
      "Adjustable lumbar support"
    ],
    "description": "Premium ergonomic office chair designed for maximum comfort...",
    "specifications": [
      "Dimensions: 27 x 27 x 41 inches",
      "Weight: 48 pounds",
      "Material: Mesh and aluminum"
    ],
    "aboutThisItem": [
      "Award-winning ergonomic design",
      "12-year warranty included",
      "Sustainable manufacturing"
    ],
    "content": "Title: Herman Miller Aeron Chair\n\nDescription: Premium ergonomic office chair...\n\nFeatures: Ergonomic design; Breathable mesh; Adjustable support...",
    "url": "https://www.amazon.sg/dp/B081H3Y55N"
  }
}
```

#### `fetch-product-image`
Download product images with proper content type handling.

**Input:**
```json
{
  "asin": "B081H3Y55N",
  "imageType": "main"
}
```

### MCP Resources

#### `product-database`
Access stored product information from the database.

**URI:** `products://database`

**Response:** Complete product database with enhanced fields including structured arrays for features, specifications, and content.

### POST /v1/fetch-image
Download binary image data from a URL.

**Request:**
```json
{
  "image_url": "https://m.media-amazon.com/images/I/image1.jpg"
}
```

**Response:** Binary image data with appropriate Content-Type header.

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 15+
- Docker (optional)

### Local Development

1. **Clone and install dependencies:**
```bash
git clone <repository>
cd mcp-server
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your database URL and other settings
```

3. **Set up database:**
```bash
npm run db:setup
```

4. **Start development server:**
```bash
npm run dev
```

5. **Test enhanced content extraction:**
```bash
npm run test:content
```

### Docker Development

1. **Start with Docker Compose:**
```bash
npm run docker:dev
```

This will start:
- PostgreSQL database
- MCP Server in development mode with hot reload
- Accessible at http://localhost:3001

### Production Deployment

1. **Build and run with Docker:**
```bash
npm run docker:run
```

2. **Or build manually:**
```bash
npm run build
npm run prisma:migrate:deploy
npm start
```

## Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run tests for CI
npm run test:ci
```

## Environment Variables

See `.env.example` for all available configuration options:

## 🗄️ Enhanced Database Schema

The application uses Prisma ORM with PostgreSQL and enhanced models for content generation:

### Product Model (Enhanced)
```prisma
model Product {
  id                String    @id @default(cuid())
  asin              String    @unique
  title             String?
  description       String?   // Product description from Amazon
  content           String?   // Comprehensive content (all fields combined)
  features          String[]  // Product features/bullets as array
  specifications    String[]  // Technical specifications as array
  aboutThisItem     String[]  // About this item highlights as array
  rating            String?   // Product rating
  reviewCount       String?   // Number of reviews
  price             Float?
  currency          String?
  mainImageUrl      String?
  productUrl        String?   // Amazon product URL
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  searchQueries     SearchQuery[]
}
```

### Supporting Models
- **SearchQuery**: Tracks search queries and associated products
- **Proxy**: Manages proxy rotation for scraping reliability
- **ScrapingLog**: Comprehensive logging of scraping activities

### Key Features
- **Array Fields**: PostgreSQL arrays for structured data (features, specifications)
- **Content Optimization**: 4000-character limit on content field for token efficiency
- **Comprehensive Storage**: All product data needed for content generation
- **Automatic Timestamps**: Created/updated tracking for data freshness

## 🎯 Content Extraction Capabilities

### Multi-Source Data Extraction
The enhanced scraper extracts comprehensive product information from multiple Amazon page sections:

- **Product Descriptions**: From `#productDescription`, `#aplus_feature_div`, and related sections
- **Feature Bullets**: Key product features and selling points
- **Technical Specifications**: Detailed product specs from specification tables
- **About This Item**: Curated product highlights and key benefits
- **Customer Data**: Ratings, review counts, and social proof
- **Pricing Information**: Current price, currency, and availability

### Content Compilation Strategy
```
Title: [Product Title]

Description: [Extracted product description up to 1000 chars]

Features: [Feature 1; Feature 2; Feature 3...]

About this item: [Highlight 1; Highlight 2; Highlight 3...]

Specifications: [Spec 1: Value 1; Spec 2: Value 2...]
```

### Token Optimization
- **Content Field**: Limited to 4000 characters for optimal AI model consumption
- **Structured Arrays**: Separate storage for easy manipulation and querying
- **Smart Filtering**: Removes irrelevant content like warnings and disclaimers
- **Fallback Handling**: Graceful degradation when certain data isn't available

### Content Generation Ready
All extracted data is optimized for:
- **Product Reviews**: Comprehensive product information for detailed reviews
- **Comparison Articles**: Structured data for product comparisons
- **Buying Guides**: Features and specifications for recommendation content
- **SEO Content**: Rich product data for search-optimized articles

## 🏗️ Architecture

- **Express.js**: Web framework with TypeScript
- **Prisma**: Type-safe database ORM
- **Zod**: Request validation and type inference
- **Cheerio**: HTML parsing and scraping
- **Axios**: HTTP client with proxy support
- **Jest**: Testing framework
- **Docker**: Containerization

## Monitoring and Logging

The server produces structured JSON logs for:
- HTTP requests and responses
- Scraping attempts and results
- Database operations
- Proxy rotation events
- Validation errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

[Add your license here]
