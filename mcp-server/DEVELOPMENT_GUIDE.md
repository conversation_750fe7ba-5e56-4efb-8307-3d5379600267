# MCP-Server 開發規範

本文件旨在為 `mcp-server` 項目提供一套統一的開發標準和流程，以確保代碼的一致性、可維護性和高品質。所有參與者在進行開發時應遵循本規範。

## 1. 技術棧 (Technology Stack)

- **運行環境**: Node.js
- **編程語言**: TypeScript
- **Web 框架**: (請填寫，例如 Express.js 或 Fastify)
- **數據庫 ORM**: Prisma
- **代碼風格**: Prettier (建議整合)
- **包管理器**: npm

## 2. 項目結構 (Project Structure)

```
mcp-server/
├── prisma/
│   ├── schema.prisma         # 數據庫模型定義
│   └── migrations/           # 數據庫遷移歷史
├── src/
│   ├── handlers/             # 請求處理層 (Controllers)
│   ├── services/             # 業務邏輯層
│   ├── lib/                  # 核心庫實例化 (如 Prisma Client)
│   ├── types/                # 全局類型定義
│   ├── utils/                # 通用工具函數
│   └── mcp-server.ts         # 服務器入口文件
├── .env                      # 本地開發環境變量 (不提交到 Git)
├── .env.example              # 環境變量範本
├── package.json              # 項目依賴與腳本
└── tsconfig.json             # TypeScript 編譯器配置
```

- **`handlers`**: 負責解析 HTTP 請求、驗證參數並調用 `services` 中的業務邏輯。每個文件應對應一個資源或路由分組。
- **`services`**: 核心業務邏輯層。處理複雜的業務規則、數據處理和與數據庫的交互。應保持與 HTTP 層解耦。
- **`lib`**: 用於初始化和導出可重用的客戶端實例，例如 `prisma.ts` 中初始化的 Prisma Client。
- **`types`**: 存放共享的 TypeScript 類型和接口，特別是那些在多個模塊中使用的數據結構。
- **`utils`**: 存放不依賴於業務邏輯的通用輔助函數，例如 `response.ts` 用於標準化 API 回應格式。

## 3. 開發流程 (Development Workflow)

### A. 環境設置

1.  複製 `.env.example` 為 `.env`。
2.  根據本地環境配置 `.env` 文件中的變量（如數據庫連接字符串）。
3.  運行 `npm install` 安裝所有依賴。

### B. 新增一個功能/API 端點

1.  **定義類型 (Types)**: 如果功能涉及新的數據結構，請先在 `src/types/index.ts` 中定義相關的 TypeScript 接口。
2.  **數據庫模型 (Prisma Schema)**: 如果需要更改數據庫，請修改 `prisma/schema.prisma`。
3.  **創建業務邏輯 (Service)**: 在 `src/services/` 目錄下創建或修改相應的 Service 文件，實現核心業務邏輯。
4.  **創建請求處理器 (Handler)**: 在 `src/handlers/` 目錄下創建一個新的 Handler 文件，用於處理請求、調用 Service 並使用 `utils/response.ts` 返回標準化結果。
5.  **註冊路由**: 在 `mcp-server.ts` (或路由配置文件) 中，將新的 Handler 綁定到對應的 API 路徑上。

### C. 數據庫遷移 (Database Migration)

**嚴禁直接修改生產數據庫。** 所有數據庫結構變更必須通過 Prisma Migrate 完成。

1.  修改 `prisma/schema.prisma` 文件。
2.  在本地運行以下命令生成新的遷移文件：
    ```bash
    npx prisma migrate dev --name <migration_name>
    ```
    `<migration_name>` 應清晰描述此次變更，例如 `add_user_profiles`。
3.  提交生成的遷移文件到 Git 倉庫。

## 4. 編碼規範 (Coding Conventions)

### A. 命名

- **文件**: 使用小寫連字符命名法 (kebab-case)，例如 `product-details.ts`。
- **變量/函數**: 使用小駝峰命名法 (camelCase)，例如 `getUserProfile`。
- **類/類型接口**: 使用大駝峰命名法 (PascalCase)，例如 `ProductDetails`。
- **常量**: 使用大寫蛇形命名法 (UPPER_SNAKE_CASE)，例如 `MAX_RETRIES`。

### B. 錯誤處理

- **Service 層**: 應拋出明確的錯誤。可以使用自定義錯誤類來區分不同類型的業務異常。
- **Handler 層**: 必須使用 `try...catch` 捕獲來自 Service 層的錯誤，並通過 `utils/response.ts` 返回統一的錯誤格式給客戶端。

### C. 日誌

- 使用 `src/services/logger.ts` 中提供的日誌服務記錄關鍵信息和錯誤。
- 避免在生產環境中使用 `console.log`。

### D. API 設計

- 所有 API 回應都應通過 `src/utils/response.ts` 中的輔助函數進行包裝，以確保返回格式的統一。

### E. 依賴管理

- 添加新的生產依賴：`npm install <package_name>`
- 添加新的開發依賴：`npm install -D <package_name>`

## 5. Git 工作流程 (Git Workflow)

- **分支**:
  - `main`: 主分支，代表生產環境的穩定代碼。
  - `develop`: 開發分支，匯集所有已完成的功能。
  - `feature/<feature_name>`: 功能開發分支，應從 `develop` 分支出來。
- **提交信息 (Commit Message)**:
  - 建議遵循 [Conventional Commits](https://www.conventionalcommits.org/) 規範。
  - 格式: `<type>(<scope>): <subject>`
  - 示例: `feat(products): add endpoint for product details`
- **合併請求 (Pull Request)**:
  - 從功能分支向 `develop` 分支發起 PR。
  - PR 需要經過代碼審查 (Code Review) 後才能合併。
