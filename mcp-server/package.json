{"name": "amazon-content-pipeline-mcp-server", "version": "2.0.0", "description": "Enhanced MCP server for Amazon.sg product scraping with comprehensive content extraction and structured data storage for content generation pipelines", "main": "build/mcp-server.js", "bin": {"amazon-scraper-mcp": "./build/mcp-server.js"}, "scripts": {"build": "tsc", "start": "node build/mcp-server.js", "dev": "ts-node-dev --respawn --transpile-only src/mcp-server.ts", "mcp": "ts-node src/mcp-server.ts", "mcp:build": "tsc && node build/mcp-server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset", "db:setup": "npm run prisma:migrate && npm run prisma:generate", "db:reset": "npm run prisma:reset && npm run prisma:generate", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "rm -rf build dist node_modules/.cache"}, "keywords": ["mcp", "amazon", "scraping", "content-generation", "product-data", "e-commerce", "singapore", "prisma", "typescript", "content-pipeline"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "@prisma/client": "^6.12.0", "axios": "^1.11.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "prisma": "^6.12.0", "puppeteer": "^24.15.0", "zod": "^3.25.76"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/cheerio": "^0.22.35", "@types/cors": "^2.8.19", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/supertest": "^6.0.3", "jest": "^30.0.5", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}