# Migration Guide: v1.x to v2.0.0

This guide helps you migrate from version 1.x to the enhanced v2.0.0 with comprehensive content extraction capabilities.

## 🔄 Overview of Changes

Version 2.0.0 introduces significant enhancements to content extraction and database storage while maintaining backward compatibility for existing functionality.

### Key Changes
- **Enhanced Database Schema**: New fields for structured content storage
- **Comprehensive Content Extraction**: Multi-source data extraction from Amazon pages
- **Improved MCP Responses**: Enhanced response formats with detailed product information
- **Content Generation Ready**: All data optimized for AI content generation workflows

## 📋 Pre-Migration Checklist

Before starting the migration, ensure you have:

- [ ] **Backup your database**: Create a full backup of your PostgreSQL database
- [ ] **Node.js 18+**: Verify you're running a supported Node.js version
- [ ] **Environment Variables**: Review and update your `.env` file
- [ ] **Dependencies**: Ensure all npm dependencies are up to date
- [ ] **Downtime Planning**: Plan for brief downtime during database migration

## 🚀 Step-by-Step Migration

### Step 1: Backup Your Data

```bash
# Create database backup
pg_dump your_database_name > backup_v1_$(date +%Y%m%d_%H%M%S).sql

# Or using Docker
docker exec your_postgres_container pg_dump -U username database_name > backup.sql
```

### Step 2: Update Dependencies

```bash
# Pull latest changes
git pull origin main

# Install updated dependencies
npm install

# Clear any cached builds
npm run clean
```

### Step 3: Run Database Migration

```bash
# Generate Prisma client with new schema
npm run prisma:generate

# Apply database migrations
npm run prisma:migrate

# Verify migration success
npm run prisma:studio
```

### Step 4: Verify Enhanced Features

```bash
# Build the application
npm run build

# Test enhanced content extraction
npm run test:content

# Run full test suite
npm test
```

### Step 5: Update Integration Code (If Applicable)

If you have custom code that consumes the MCP server responses, you may need to update it to handle the new response format.

## 📊 Database Schema Changes

### New Fields Added to Product Model

```sql
-- New fields added in migration
ALTER TABLE "Product" ADD COLUMN "aboutThisItem" TEXT[];
ALTER TABLE "Product" ADD COLUMN "features" TEXT[];
ALTER TABLE "Product" ADD COLUMN "productUrl" TEXT;
ALTER TABLE "Product" ADD COLUMN "rating" TEXT;
ALTER TABLE "Product" ADD COLUMN "reviewCount" TEXT;
ALTER TABLE "Product" ADD COLUMN "specifications" TEXT[];
```

### Field Mapping

| Old Field | New Field | Type | Description |
|-----------|-----------|------|-------------|
| `description` | `description` | String | Enhanced with better extraction |
| `content` | `content` | String | Now comprehensive 4000-char content |
| N/A | `features` | String[] | Product features as array |
| N/A | `specifications` | String[] | Technical specs as array |
| N/A | `aboutThisItem` | String[] | Product highlights as array |
| N/A | `rating` | String | Product rating |
| N/A | `reviewCount` | String | Number of reviews |
| N/A | `productUrl` | String | Full Amazon URL |

## 🔧 API Response Changes

### Enhanced ProductDetailsResult

**Before (v1.x):**
```json
{
  "success": true,
  "asin": "B081H3Y55N",
  "product": {
    "title": "Product Title",
    "price": { "amount": 100, "currency": "SGD" },
    "features": ["Feature 1", "Feature 2"],
    "url": "https://amazon.sg/dp/B081H3Y55N"
  }
}
```

**After (v2.0.0):**
```json
{
  "success": true,
  "asin": "B081H3Y55N",
  "product": {
    "title": "Product Title",
    "price": { "amount": 100, "currency": "SGD", "formatted": "SGD 100" },
    "rating": { "value": "4.5", "formatted": "4.5/5" },
    "reviews": { "count": "1,234", "formatted": "1,234 ratings" },
    "features": ["Feature 1", "Feature 2"],
    "description": "Detailed product description...",
    "specifications": ["Spec 1: Value 1", "Spec 2: Value 2"],
    "aboutThisItem": ["Highlight 1", "Highlight 2"],
    "content": "Comprehensive content with all fields combined...",
    "url": "https://amazon.sg/dp/B081H3Y55N"
  }
}
```

## ⚠️ Breaking Changes

### Response Format Changes
- **New Fields**: Additional fields in ProductDetailsResult response
- **Enhanced Arrays**: Features now part of larger structured response
- **Content Field**: Enhanced content compilation format

### Database Schema Changes
- **New Columns**: Six new columns added to Product table
- **Array Types**: PostgreSQL text arrays for structured data
- **Data Types**: Some fields changed from simple strings to structured data

## 🔄 Backward Compatibility

### What Still Works
- ✅ All existing MCP tools (`search-amazon-products`, `get-product-details`, `fetch-product-image`)
- ✅ All existing API endpoints
- ✅ Existing database records (automatically migrated)
- ✅ Docker deployment configurations
- ✅ Environment variable configurations

### What Might Need Updates
- ⚠️ Custom code parsing ProductDetailsResult responses
- ⚠️ Database queries directly accessing Product table
- ⚠️ Content processing pipelines expecting old format

## 🧪 Testing Your Migration

### Verify Core Functionality
```bash
# Test product search
curl -X POST http://localhost:3000/mcp/tools/search-amazon-products \
  -H "Content-Type: application/json" \
  -d '{"topic": "office chairs", "limit": 5}'

# Test enhanced product details
curl -X POST http://localhost:3000/mcp/tools/get-product-details \
  -H "Content-Type: application/json" \
  -d '{"asin": "B081H3Y55N"}'
```

### Verify Database Changes
```sql
-- Check new columns exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'Product';

-- Verify array fields
SELECT asin, array_length(features, 1) as feature_count 
FROM "Product" 
WHERE features IS NOT NULL;
```

## 🆘 Rollback Procedure

If you need to rollback to v1.x:

1. **Stop the application**
2. **Restore database backup**:
   ```bash
   psql your_database_name < backup_v1_YYYYMMDD_HHMMSS.sql
   ```
3. **Checkout previous version**:
   ```bash
   git checkout v1.x.x
   npm install
   npm run build
   ```

## 📞 Support

If you encounter issues during migration:

1. **Check the logs**: Review application and database logs for errors
2. **Verify environment**: Ensure all environment variables are correct
3. **Test incrementally**: Test each step of the migration process
4. **Consult documentation**: Review the updated README.md and API documentation

## 🎉 Post-Migration Benefits

After successful migration, you'll have access to:

- **Enhanced Content Extraction**: Comprehensive product data for content generation
- **Structured Data Storage**: PostgreSQL arrays for easy querying and manipulation
- **Content Generation Ready**: All data optimized for AI workflows
- **Improved Performance**: Better caching and data organization
- **Future-Proof Architecture**: Scalable design for additional enhancements
